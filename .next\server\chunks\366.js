exports.id=366,exports.ids=[366],exports.modules={4862:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,2583,23)),Promise.resolve().then(a.t.bind(a,6840,23)),Promise.resolve().then(a.t.bind(a,8771,23)),Promise.resolve().then(a.t.bind(a,3225,23)),Promise.resolve().then(a.t.bind(a,9295,23)),Promise.resolve().then(a.t.bind(a,3982,23))},7731:(e,t,a)=>{Promise.resolve().then(a.bind(a,3532))},5590:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var r=a(2295),l=a(1223),s=a.n(l),n=a(783),i=a.n(n),d=a(2859);function o(){return(0,r.jsxs)("footer",{className:"relative w-full bg-brand-background text-bauhaus-black overflow-hidden",children:[r.jsx("div",{className:"absolute inset-0 opacity-20",children:r.jsx(d.AnimatedSoftGrid,{className:"w-full h-full text-black",opacity:"default",animationPreset:"drift",animationIndex:200})}),r.jsx("div",{className:"relative z-10 px-6 md:px-12 lg:px-24 py-16 md:py-20",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-12 gap-12 lg:gap-16 mb-16",children:[(0,r.jsxs)("div",{className:"lg:col-span-5 space-y-6",children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx(s(),{src:"/images/logo.png",alt:"Navhaus",width:140,height:45,className:"h-10 w-auto"}),r.jsx("div",{className:"absolute -top-2 -right-8",children:r.jsx(d.AnimatedSoftCircle,{size:"sm",color:"red",className:"w-4 h-4",animationPreset:"gentle",animationIndex:201})})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("p",{className:"text-xl font-bold text-bauhaus-black",children:"What matters, made real."}),r.jsx("p",{className:"text-gray-700 leading-relaxed max-w-sm",children:"We build bold, efficient, and meaningful digital experiences. Nothing more, nothing less."})]})]}),r.jsx("div",{className:"lg:col-span-3 lg:mt-16",children:(0,r.jsxs)("nav",{className:"space-y-4",children:[r.jsx(i(),{href:"/",className:"block text-bauhaus-black hover:text-bauhaus-red transition-colors duration-200 font-medium",children:"Home"}),r.jsx(i(),{href:"/about",className:"block text-bauhaus-black hover:text-bauhaus-red transition-colors duration-200 font-medium",children:"About"}),r.jsx(i(),{href:"/work",className:"block text-bauhaus-black hover:text-bauhaus-red transition-colors duration-200 font-medium",children:"Work"}),r.jsx(i(),{href:"/contact",className:"block text-bauhaus-black hover:text-bauhaus-red transition-colors duration-200 font-medium",children:"Contact"})]})}),(0,r.jsxs)("div",{className:"lg:col-span-4 space-y-8 lg:mt-16",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx("p",{className:"text-bauhaus-black font-medium",children:"Ready to build something?"}),r.jsx(i(),{href:"/contact",className:"inline-block px-4 py-2 border-2 border-bauhaus-black bg-transparent text-bauhaus-black font-bold uppercase tracking-wide hover:bg-bauhaus-black hover:text-brand-background transition-colors duration-200 rounded-xl text-sm",children:"Start here"})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2 text-sm text-gray-600",children:[(0,r.jsxs)("div",{className:"flex space-x-1",children:[r.jsx("div",{className:"w-2 h-2 bg-bauhaus-red rounded-full"}),r.jsx("div",{className:"w-2 h-2 bg-bauhaus-yellow rounded-full"}),r.jsx("div",{className:"w-2 h-2 bg-bauhaus-blue rounded-full"})]}),r.jsx("span",{children:"Usually responds within 24 hours"})]})]})]}),(0,r.jsxs)("div",{className:"relative py-8 mb-12",children:[r.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:r.jsx("div",{className:"w-full h-px bg-gray-800"})}),(0,r.jsxs)("div",{className:"relative flex justify-center space-x-8",children:[r.jsx(d.AnimatedRoundedRectangle,{width:"lg",height:"sm",color:"red",className:"w-16 h-4",animationPreset:"flowing",animationIndex:205}),r.jsx(d.AnimatedSoftCircle,{size:"md",color:"yellow",className:"w-8 h-8",animationPreset:"pulse",animationIndex:206}),r.jsx(d.AnimatedTriangle,{size:"md",color:"blue",direction:"up",className:"w-8 h-8",animationPreset:"dynamic",animationIndex:207})]})]}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["\xa9 ",new Date().getFullYear()," Navhaus. All rights reserved."]}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-600",children:[r.jsx("span",{children:"Built with intention"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("div",{className:"w-2 h-2 bg-bauhaus-red rounded-full"}),r.jsx("div",{className:"w-2 h-2 bg-bauhaus-yellow rounded-full"}),r.jsx("div",{className:"w-2 h-2 bg-bauhaus-blue rounded-full"})]})]})]})]})}),r.jsx("div",{className:"absolute top-16 left-16 opacity-15",children:r.jsx(d.AnimatedBlob,{color:"red",className:"w-24 h-24",animationPreset:"drift",animationIndex:208})}),r.jsx("div",{className:"absolute bottom-20 right-20 opacity-10",children:r.jsx(d.AnimatedQuarterCircle,{color:"blue",corner:"top-left",className:"w-32 h-32",animationPreset:"gentle",animationIndex:209})}),r.jsx("div",{className:"absolute top-1/2 right-8 opacity-8",children:r.jsx(d.AnimatedRoundedRectangle,{width:"lg",height:"xl",color:"yellow",className:"w-8 h-24",animationPreset:"float",animationIndex:210})}),r.jsx("div",{className:"absolute bottom-8 left-1/4 opacity-12",children:r.jsx(d.AnimatedSoftCircle,{size:"lg",color:"red",className:"w-16 h-16",animationPreset:"energetic",animationIndex:211})}),r.jsx("div",{className:"absolute top-20 right-1/3 opacity-8",children:r.jsx(d.AnimatedTriangle,{size:"lg",color:"yellow",direction:"up",className:"w-12 h-12",animationPreset:"pulse",animationIndex:212})})]})}},9705:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(2295),l=a(3729),s=a(783),n=a.n(s),i=a(1223),d=a.n(i),o=a(2254);function c(){let[e,t]=(0,l.useState)(!1),a=(0,o.usePathname)(),s=[{href:"/",label:"Home"},{href:"/about",label:"About"},{href:"/contact",label:"Contact"}];return r.jsx("header",{className:"w-full py-6 px-6 md:px-12 lg:px-24",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("nav",{className:"flex justify-between items-center",children:[r.jsx(n(),{href:"/",className:"flex items-center",children:r.jsx(d(),{src:"/images/logo.png",alt:"Navhaus",width:120,height:40,className:"h-8 w-auto lg:-mt-[15px]"})}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[s.map(e=>r.jsx(n(),{href:e.href,className:`font-medium uppercase tracking-wide transition-colors duration-200 ${a===e.href?"text-bauhaus-red":"text-bauhaus-black hover:text-bauhaus-red"}`,children:e.label},e.href)),r.jsx(n(),{href:"/contact",className:"btn-primary ml-8",children:"Start Project"})]}),r.jsx("button",{className:"md:hidden text-bauhaus-black",onClick:()=>t(!e),children:r.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e?r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})]}),e&&r.jsx("div",{className:"md:hidden mt-6 py-6 border-t border-bauhaus-black",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-4",children:[s.map(e=>r.jsx(n(),{href:e.href,className:`font-medium uppercase tracking-wide transition-colors duration-200 ${a===e.href?"text-bauhaus-red":"text-bauhaus-black hover:text-bauhaus-red"}`,onClick:()=>t(!1),children:e.label},e.href)),r.jsx(n(),{href:"/contact",className:"btn-primary inline-block mt-4",onClick:()=>t(!1),children:"Start Project"})]})})]})})}},3532:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var r=a(3729);function l(){return(0,r.useEffect)(()=>{},[]),null}},2859:(e,t,a)=>{"use strict";a.r(t),a.d(t,{AnimatedBlob:()=>A,AnimatedCircle:()=>w,AnimatedHalfCircle:()=>k,AnimatedPill:()=>P,AnimatedQuarterCircle:()=>C,AnimatedRectangle:()=>j,AnimatedRoundedRect:()=>S,AnimatedRoundedRectangle:()=>y,AnimatedSoftCircle:()=>v,AnimatedSoftGrid:()=>$,AnimatedTriangle:()=>N});var r=a(2295),l=a(3729);function s(e){let[t,a]=(0,l.useState)(0),r=(0,l.useRef)(null),[s,n]=(0,l.useState)({x:0,y:0}),[i,d]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{let t=setTimeout(()=>{let t=e.randomSeed||Math.random(),a=43758.5453*Math.sin(45.164*t)%1*Math.PI*2,r=.8+.4*Math.abs(43758.5453*Math.sin(12.9898*t)%1);n({x:Math.cos(a)*r,y:Math.sin(a)*r}),d(!0)},50);return()=>clearTimeout(t)},[e.randomSeed]),(0,l.useEffect)(()=>{if(!i)return;let e=()=>{a(window.scrollY)};return window.addEventListener("scroll",e,{passive:!0}),()=>{window.removeEventListener("scroll",e)}},[i]),{ref:r,style:{transform:(()=>{var a;if(!r.current||!i)return"translate3d(0, 0, 0)";let l=r.current.getBoundingClientRect(),n=l.top+t,d=(a=Math.max(0,Math.min(1,(t-n+window.innerHeight)/(window.innerHeight+l.height))))<.5?2*a*a:-1+(4-2*a)*a,o=0,c=0;return("x"===e.direction||"both"===e.direction)&&(o=d*e.intensity*s.x*e.speed),("y"===e.direction||"both"===e.direction)&&(c=d*e.intensity*s.y*e.speed),`translate3d(${o}px, ${c}px, 0)`})(),transition:"transform 0.1s ease-out",willChange:"transform"}}}let n={subtle:{direction:"both",intensity:20,speed:.6},gentle:{direction:"both",intensity:30,speed:.8},dynamic:{direction:"both",intensity:45,speed:1.2},flowing:{direction:"both",intensity:35,speed:.9},energetic:{direction:"both",intensity:50,speed:1.4},drift:{direction:"both",intensity:25,speed:.5},pulse:{direction:"both",intensity:35,speed:1.1},float:{direction:"both",intensity:15,speed:.4},horizontal:{direction:"x",intensity:30,speed:.8}};var i=a(2547),d=a(5084);let o={sm:"w-0 h-0 border-l-[16px] border-r-[16px] border-b-[28px]",md:"w-0 h-0 border-l-[24px] border-r-[24px] border-b-[42px]",lg:"w-0 h-0 border-l-[32px] border-r-[32px] border-b-[56px]",xl:"w-0 h-0 border-l-[48px] border-r-[48px] border-b-[84px]"},c=(e,t,a)=>{let r=o[e];switch(a){case"up":default:return`${r} border-l-transparent border-r-transparent`;case"down":return r.replace("border-b-","border-t-")+" border-l-transparent border-r-transparent";case"left":return r.replace("border-l-","border-r-").replace("border-r-","border-t-").replace("border-b-","border-l-")+" border-t-transparent border-b-transparent";case"right":return r.replace("border-r-","border-l-").replace("border-l-","border-t-").replace("border-b-","border-r-")+" border-t-transparent border-b-transparent"}};function u({size:e="md",color:t="yellow",direction:a="up",className:l=""}){let s=c(e,t,a);return r.jsx("div",{className:`${s} ${l}`,style:{borderBottomColor:"up"===a?"red"===t?"#e94436":"yellow"===t?"#ffc527":"blue"===t?"#434897":"black"===t?"#000000":"#ffffff":"transparent",borderTopColor:"down"===a?"red"===t?"#e94436":"yellow"===t?"#ffc527":"blue"===t?"#434897":"black"===t?"#000000":"#ffffff":"transparent",borderLeftColor:"right"===a?"red"===t?"#e94436":"yellow"===t?"#ffc527":"blue"===t?"#434897":"black"===t?"#000000":"#ffffff":"transparent",borderRightColor:"left"===a?"red"===t?"#e94436":"yellow"===t?"#ffc527":"blue"===t?"#434897":"black"===t?"#000000":"#ffffff":"transparent"}})}let h={sm:"w-8 h-4",md:"w-16 h-8",lg:"w-24 h-12",xl:"w-32 h-16"},b={red:"bg-bauhaus-red",yellow:"bg-bauhaus-yellow",blue:"bg-bauhaus-blue",black:"bg-bauhaus-black",white:"bg-bauhaus-white border border-bauhaus-black"},m={top:"rounded-t-full",bottom:"rounded-b-full",left:"rounded-l-full",right:"rounded-r-full"},f=e=>{switch(e){case"left":case"right":return"w-4 h-8 md:w-8 md:h-16 lg:w-12 lg:h-24 xl:w-16 xl:h-32";default:return""}};function x({size:e="md",color:t="red",direction:a="top",className:l=""}){let s=f(a)||h[e];return r.jsx("div",{className:`${s} ${b[t]} ${m[a]} ${l}`})}var g=a(6991);let p=(e,t=0)=>{let a=0,r=`${e}-${t}`;for(let e=0;e<r.length;e++)a=(a<<5)-a+r.charCodeAt(e),a&=a;return Math.abs(a)/2147483647};function w({animationPreset:e="gentle",animationIndex:t=0,...a}){let l=s({...n[e],randomSeed:p("circle",t)});return r.jsx("div",{ref:l.ref,className:"h-full",style:l.style,children:r.jsx(i.Z,{...a})})}function v({animationPreset:e="subtle",animationIndex:t=0,...a}){let l=s({...n[e],randomSeed:p("soft-circle",t)});return r.jsx("div",{ref:l.ref,className:"h-full",style:l.style,children:r.jsx(i.H,{...a})})}function j({animationPreset:e="gentle",animationIndex:t=0,...a}){let l=s({...n[e],randomSeed:p("rectangle",t)});return r.jsx("div",{ref:l.ref,className:"h-full",style:l.style,children:r.jsx(d.ZP,{...a})})}function y({animationPreset:e="gentle",animationIndex:t=0,...a}){let l=s({...n[e],randomSeed:p("rounded-rectangle",t)});return r.jsx("div",{ref:l.ref,className:"h-full",style:l.style,children:r.jsx(d.c9,{...a})})}function N({animationPreset:e="dynamic",animationIndex:t=0,...a}){let l=s({...n[e],randomSeed:p("triangle",t)});return r.jsx("div",{ref:l.ref,className:"h-full",style:l.style,children:r.jsx(u,{...a})})}function k({animationPreset:e="gentle",animationIndex:t=0,...a}){let l=s({...n[e],randomSeed:p("half-circle",t)});return r.jsx("div",{ref:l.ref,className:"h-full",style:l.style,children:r.jsx(x,{...a})})}function $({animationPreset:e="subtle",animationIndex:t=0,opacity:a="default",...l}){let i=s({...n[e],randomSeed:p("soft-grid",t)});return r.jsx("div",{ref:i.ref,className:"h-full",style:i.style,children:r.jsx(g.Xg,{opacity:a,...l})})}function S({animationPreset:e="gentle",animationIndex:t=0,...a}){let l=s({...n[e],randomSeed:p("rounded-rect",t)});return r.jsx("div",{ref:l.ref,className:"h-full",style:l.style,children:r.jsx(g.uM,{...a})})}function P({animationPreset:e="horizontal",animationIndex:t=0,...a}){let l=s({...n[e],randomSeed:p("pill",t)});return r.jsx("div",{ref:l.ref,className:"h-full",style:l.style,children:r.jsx(g.DR,{...a})})}function A({animationPreset:e="dynamic",animationIndex:t=0,...a}){let l=s({...n[e],randomSeed:p("blob",t)});return r.jsx("div",{ref:l.ref,className:"h-full",style:l.style,children:r.jsx(g.t6,{...a})})}function C({animationPreset:e="gentle",animationIndex:t=0,...a}){let l=s({...n[e],randomSeed:p("quarter-circle",t)});return r.jsx("div",{ref:l.ref,className:"h-full",style:l.style,children:r.jsx(g.kw,{...a})})}},2547:(e,t,a)=>{"use strict";a.d(t,{H:()=>i,Z:()=>n});var r=a(2295);let l={sm:"w-8 h-8",md:"w-16 h-16",lg:"w-24 h-24",xl:"w-32 h-32"},s={red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black",white:"bg-white border border-black"};function n({size:e="md",color:t="red",className:a=""}){return r.jsx("div",{className:`rounded-full ${l[e]} ${s[t]} ${a}`})}function i({size:e="md",color:t="red",className:a=""}){return r.jsx("div",{className:`rounded-full ${l[e]} ${s[t]} ${a}`,style:{filter:"blur(0.5px)"}})}},5084:(e,t,a)=>{"use strict";a.d(t,{ZP:()=>i,c9:()=>d});var r=a(2295);let l={sm:"w-12",md:"w-24",lg:"w-32",xl:"w-48"},s={sm:"h-8",md:"h-16",lg:"h-24",xl:"h-32"},n={red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black",white:"bg-white border border-black"};function i({width:e="md",height:t="md",color:a="blue",className:i=""}){return r.jsx("div",{className:`${l[e]} ${s[t]} ${n[a]} ${i}`})}function d({width:e="md",height:t="md",color:a="blue",className:i=""}){return r.jsx("div",{className:`rounded-3xl ${l[e]} ${s[t]} ${n[a]} ${i}`})}},6991:(e,t,a)=>{"use strict";a.d(t,{B$:()=>i,DR:()=>s,Xg:()=>o,kw:()=>d,t6:()=>n,uM:()=>l});var r=a(2295);function l({className:e="",color:t="blue"}){return r.jsx("div",{className:`${{red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[t]} rounded-3xl ${e}`})}function s({className:e="",color:t="yellow"}){return r.jsx("div",{className:`${{red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[t]} rounded-full ${e}`})}function n({className:e="",color:t="red"}){return r.jsx("div",{className:`${{red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[t]} ${e}`,style:{borderRadius:"60% 40% 30% 70% / 60% 30% 70% 40%"}})}function i({className:e="",color:t="blue",direction:a="right"}){return r.jsx("div",{className:`${{red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[t]} ${{right:"rounded-l-full",left:"rounded-r-full",top:"rounded-b-full",bottom:"rounded-t-full"}[a]} ${e}`})}function d({className:e="",color:t="yellow",corner:a="top-left"}){return r.jsx("div",{className:`${{red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[t]} ${{"top-left":"rounded-br-full","top-right":"rounded-bl-full","bottom-left":"rounded-tr-full","bottom-right":"rounded-tl-full"}[a]} ${e}`})}function o({className:e="",opacity:t="default"}){return r.jsx("div",{className:`absolute inset-0 ${"hero"===t?"opacity-40":"opacity-20"} ${e}`,children:(0,r.jsxs)("svg",{width:"100%",height:"100%",className:"w-full h-full",children:[r.jsx("defs",{children:r.jsx("pattern",{id:"grid",width:"40",height:"40",patternUnits:"userSpaceOnUse",children:r.jsx("path",{d:"M 40 0 L 0 0 0 40",fill:"none",stroke:"currentColor",strokeWidth:"1",opacity:"hero"===t?"0.5":"0.3"})})}),r.jsx("rect",{width:"100%",height:"100%",fill:"url(#grid)"})]})})}},7843:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u,metadata:()=>c});var r=a(5036),l=a(448),s=a.n(l);a(5023);let n=(0,a(6843).createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\performance\PerformanceMonitor.tsx`),{__esModule:i,$$typeof:d}=n,o=n.default,c={title:{default:"navhaus | what matters, made real",template:"%s | navhaus"},description:"Navhaus is a design and development studio that builds bold, efficient, and meaningful digital experiences - nothing more, nothing less.",keywords:["web design","web development","digital agency","wordpress","web apps","navhaus"],authors:[{name:"Navhaus"}],creator:"Navhaus",publisher:"Navhaus",metadataBase:new URL("https://navhaus.com"),alternates:{canonical:"/"},openGraph:{title:"navhaus | what matters, made real",description:"Navhaus is a design and development studio that builds bold, efficient, and meaningful digital experiences - nothing more, nothing less.",url:"https://navhaus.com",siteName:"Navhaus",images:[{url:"/images/og-image.png",width:1200,height:630,alt:"Navhaus - what matters, made real"}],locale:"en_US",type:"website"},twitter:{card:"summary_large_image",title:"navhaus | what matters, made real",description:"Navhaus is a design and development studio that builds bold, efficient, and meaningful digital experiences - nothing more, nothing less.",images:["/images/og-image.png"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},icons:{icon:"/images/icon.png",shortcut:"/images/icon.png",apple:"/images/icon.png"}};function u({children:e}){return(0,r.jsxs)("html",{lang:"en",className:s().variable,children:[r.jsx("head",{children:r.jsx("link",{rel:"dns-prefetch",href:"https://fonts.googleapis.com"})}),(0,r.jsxs)("body",{className:s().className,children:[r.jsx(o,{}),e]})]})}},5023:()=>{}};