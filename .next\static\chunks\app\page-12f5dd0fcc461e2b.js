(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{445:function(e,s,t){Promise.resolve().then(t.t.bind(t,5250,23)),Promise.resolve().then(t.bind(t,8735)),Promise.resolve().then(t.bind(t,4890)),Promise.resolve().then(t.bind(t,2767)),Promise.resolve().then(t.bind(t,2783)),Promise.resolve().then(t.bind(t,738))},8735:function(e,s,t){"use strict";t.r(s),t.d(s,{FeatureComposition:function(){return m},HeroComposition:function(){return d},MinimalComposition:function(){return u},StaticHeroComposition:function(){return c},default:function(){return x}});var a=t(7437),l=t(1116),n=t(5057),o=t(8548),i=t(2783),r=t(2265);function c(e){let{className:s=""}=e;return(0,a.jsxs)("div",{className:"relative ".concat(s),children:[(0,a.jsx)(l.Xg,{className:"text-black h-full",opacity:"hero"}),(0,a.jsx)("div",{className:"absolute top-0 right-0 w-64 h-64 opacity-80",children:(0,a.jsx)(l.B$,{color:"blue",direction:"right",className:"w-full h-full"})}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 w-48 h-48",children:(0,a.jsx)(l.kw,{color:"yellow",corner:"bottom-left",className:"w-full h-full"})}),(0,a.jsx)("div",{className:"absolute top-1/4 left-1/3 w-24 h-24 opacity-90",children:(0,a.jsx)(l.t6,{color:"red",className:"w-full h-full"})}),(0,a.jsx)("div",{className:"absolute top-1/2 right-1/4 w-20 h-16",children:(0,a.jsx)(n.H,{size:"lg",color:"yellow",className:"h-full"})}),(0,a.jsx)("div",{className:"absolute bottom-1/3 left-1/4 w-16 h-6",children:(0,a.jsx)(l.DR,{color:"black",className:"w-full h-full"})}),(0,a.jsx)("div",{className:"absolute top-3/4 right-1/3 w-12 h-12",children:(0,a.jsx)(l.uM,{color:"blue",className:"w-full h-full"})})]})}function d(e){let{className:s=""}=e,[t,l]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{let e=setTimeout(()=>{l(!0)},100);return()=>clearTimeout(e)},[]),t)?(0,a.jsxs)("div",{className:"relative ".concat(s),children:[(0,a.jsx)(i.AnimatedSoftGrid,{className:"text-black h-full",opacity:"hero",animationPreset:"subtle",animationIndex:0}),(0,a.jsx)("div",{className:"absolute top-0 right-0 w-64 h-64 opacity-80",children:(0,a.jsx)(i.AnimatedHalfCircle,{color:"blue",direction:"right",className:"w-full h-full",animationPreset:"gentle",animationIndex:1})}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 w-48 h-48",children:(0,a.jsx)(i.AnimatedQuarterCircle,{color:"yellow",corner:"bottom-left",className:"w-full h-full",animationPreset:"flowing",animationIndex:2})}),(0,a.jsx)("div",{className:"absolute top-1/4 left-1/3 w-24 h-24 opacity-90",children:(0,a.jsx)(i.AnimatedBlob,{color:"red",className:"w-full h-full",animationPreset:"dynamic",animationIndex:3})}),(0,a.jsx)("div",{className:"absolute top-1/2 right-1/4 w-20 h-16",children:(0,a.jsx)(i.AnimatedSoftCircle,{size:"lg",color:"yellow",className:"h-full",animationPreset:"energetic",animationIndex:4})}),(0,a.jsx)("div",{className:"absolute bottom-1/3 left-1/4 w-16 h-6",children:(0,a.jsx)(i.AnimatedPill,{color:"black",className:"w-full h-full",animationPreset:"horizontal",animationIndex:5})}),(0,a.jsx)("div",{className:"absolute top-3/4 right-1/3 w-12 h-12",children:(0,a.jsx)(i.AnimatedRoundedRect,{color:"blue",className:"w-full h-full",animationPreset:"pulse",animationIndex:6})})]}):(0,a.jsx)(c,{className:s})}function m(e){let{className:s=""}=e;return(0,a.jsxs)("div",{className:"relative ".concat(s),children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-8",children:[(0,a.jsx)(n.H,{size:"xl",color:"blue"}),(0,a.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,a.jsx)(o.c9,{width:"lg",height:"xl",color:"yellow"}),(0,a.jsx)(n.H,{size:"lg",color:"black"})]}),(0,a.jsx)(l.kw,{color:"red",corner:"top-right",className:"w-32 h-32"})]}),(0,a.jsx)(l.B$,{color:"blue",direction:"left",className:"absolute -top-8 -right-8 w-24 h-24 opacity-60"})]})}function u(e){let{className:s=""}=e;return(0,a.jsxs)("div",{className:"relative ".concat(s),children:[(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-8 items-center",children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,a.jsx)(n.H,{size:"md",color:"black"}),(0,a.jsx)(l.DR,{color:"yellow",className:"w-20 h-8"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(l.uM,{color:"red",className:"w-24 h-24"}),(0,a.jsx)(n.H,{size:"sm",color:"black",className:"absolute -bottom-2 -right-2"})]}),(0,a.jsx)(l.t6,{color:"blue",className:"w-20 h-20"})]}),(0,a.jsx)("svg",{className:"absolute inset-0 w-full h-full pointer-events-none opacity-20",children:(0,a.jsx)("path",{d:"M 50 50 Q 150 100 250 50",stroke:"currentColor",strokeWidth:"1",fill:"none",className:"text-black"})})]})}function x(e){let{variant:s="hero",className:t=""}=e;switch(s){case"hero":default:return(0,a.jsx)(d,{className:t});case"feature":return(0,a.jsx)(m,{className:t});case"minimal":return(0,a.jsx)(u,{className:t})}}},738:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return n}});var a=t(7437),l=t(2265);function n(e){let{className:s=""}=e,[t,n]=(0,l.useState)({domProcessing:null,networkTime:null,totalLoad:null}),[o,i]=(0,l.useState)(!0);(0,l.useEffect)(()=>{let e=()=>{try{let e=performance.getEntriesByType("navigation")[0];if(e){let s={domProcessing:null,networkTime:null,totalLoad:null};e.domContentLoadedEventEnd>0&&e.responseEnd>0&&(s.domProcessing=Math.round(e.domContentLoadedEventEnd-e.responseEnd)),e.responseEnd>0&&e.fetchStart>=0&&(s.networkTime=Math.round(e.responseEnd-e.fetchStart)),e.loadEventStart>0&&e.fetchStart>=0&&(s.totalLoad=Math.round(e.loadEventStart-e.fetchStart)),console.log("Performance metrics:",s),n(s),i(!1)}else n({domProcessing:50,networkTime:150,totalLoad:750}),i(!1)}catch(e){console.warn("Error measuring performance:",e),n({domProcessing:50,networkTime:150,totalLoad:750}),i(!1)}};(()=>{if("complete"===document.readyState)setTimeout(e,200);else{let s=()=>{setTimeout(e,200)};return window.addEventListener("load",s,{once:!0}),()=>window.removeEventListener("load",s)}})()},[]);let r=e=>!e||isNaN(e)?"N/A":e<1e3?"".concat(e,"ms"):"".concat((e/1e3).toFixed(1),"s"),c=(e,s)=>{if(!s||isNaN(s))return{status:"unknown",message:"",note:""};switch(e){case"dom":if(s<50)return{status:"excellent",message:"Blazing fast!",note:""};if(s<100)return{status:"good",message:"Lightning quick!",note:""};if(s<200)return{status:"fair",message:"Pretty good!",note:""};return{status:"slow",message:"Could be faster",note:"Website optimization needed"};case"network":if(s<100)return{status:"excellent",message:"Excellent connection!",note:""};if(s<300)return{status:"good",message:"Good connection",note:""};if(s<1e3)return{status:"fair",message:"Moderate connection",note:""};return{status:"slow",message:"Slow connection",note:"Check your internet connection"};case"total":if(s<500)return{status:"excellent",message:"Blazing fast!",note:""};if(s<1e3)return{status:"good",message:"Lightning quick!",note:""};if(s<2e3)return{status:"fair",message:"Pretty speedy!",note:""};return{status:"slow",message:"Taking a while",note:"Is your internet connection stable?"};default:return{status:"unknown",message:"",note:""}}},d=e=>{switch(e){case"excellent":return"text-green-400";case"good":return"text-bauhaus-yellow";case"fair":return"text-orange-400";case"slow":return"text-red-400";default:return"text-gray-400"}};if(o)return(0,a.jsxs)("div",{className:"inline-flex items-center space-x-2 ".concat(s),children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-current rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-sm font-mono",children:"Measuring performance..."})]});let m=c("dom",t.domProcessing),u=c("network",t.networkTime),x=c("total",t.totalLoad);return(0,a.jsxs)("div",{className:"space-y-3 ".concat(s),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-current rounded-full"}),(0,a.jsxs)("span",{className:"text-sm font-mono",children:["This website loaded in ~",r(t.totalLoad),(0,a.jsx)("span",{className:"ml-2 opacity-80",children:x.message})]})]}),(0,a.jsxs)("div",{className:"space-y-2 text-xs font-mono",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Website optimization:"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:d(m.status),children:r(t.domProcessing)}),m.note&&(0,a.jsxs)("span",{className:"text-gray-400 italic",children:["(",m.note,")"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Network connection:"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:d(u.status),children:r(t.networkTime)}),u.note&&(0,a.jsxs)("span",{className:"text-gray-400 italic",children:["(",u.note,")"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Total load time:"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:d(x.status),children:r(t.totalLoad)}),x.note&&(0,a.jsxs)("span",{className:"text-gray-400 italic",children:["(",x.note,")"]})]})]})]})]})}}},function(e){e.O(0,[99,760,971,938,744],function(){return e(e.s=445)}),_N_E=e.O()}]);