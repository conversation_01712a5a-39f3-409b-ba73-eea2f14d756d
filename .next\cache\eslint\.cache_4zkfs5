[{"C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\page.tsx": "4", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\work\\page.tsx": "5", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\compositions\\OrganicComposition.tsx": "6", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\Footer.tsx": "7", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\Header.tsx": "8", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\PageWrapper.tsx": "9", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\performance\\PerformanceMonitor.tsx": "10", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\AnimatedShapes.tsx": "11", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Circle.tsx": "12", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\HalfCircle.tsx": "13", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Rectangle.tsx": "14", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\RoundedShapes.tsx": "15", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Triangle.tsx": "16", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\hooks\\useScrollAnimation.ts": "17", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\sitemap.ts": "18", "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\ui\\LoadTimeDisplay.tsx": "19"}, {"size": 26446, "mtime": 1751892145239, "results": "20", "hashOfConfig": "21"}, {"size": 13259, "mtime": 1751894932503, "results": "22", "hashOfConfig": "21"}, {"size": 2541, "mtime": 1751893390071, "results": "23", "hashOfConfig": "21"}, {"size": 51710, "mtime": 1751889303597, "results": "24", "hashOfConfig": "21"}, {"size": 5956, "mtime": 1751366727918, "results": "25", "hashOfConfig": "21"}, {"size": 7017, "mtime": 1751465060420, "results": "26", "hashOfConfig": "21"}, {"size": 7580, "mtime": 1751889205408, "results": "27", "hashOfConfig": "21"}, {"size": 3272, "mtime": 1751892294953, "results": "28", "hashOfConfig": "21"}, {"size": 419, "mtime": 1751363944301, "results": "29", "hashOfConfig": "21"}, {"size": 1341, "mtime": 1751893478893, "results": "30", "hashOfConfig": "21"}, {"size": 6976, "mtime": 1751403724820, "results": "31", "hashOfConfig": "21"}, {"size": 949, "mtime": 1751366318919, "results": "32", "hashOfConfig": "21"}, {"size": 1302, "mtime": 1751363912836, "results": "33", "hashOfConfig": "21"}, {"size": 1411, "mtime": 1751366358877, "results": "34", "hashOfConfig": "21"}, {"size": 3823, "mtime": 1751402711577, "results": "35", "hashOfConfig": "21"}, {"size": 2642, "mtime": 1751365661262, "results": "36", "hashOfConfig": "21"}, {"size": 4238, "mtime": 1751885280725, "results": "37", "hashOfConfig": "21"}, {"size": 669, "mtime": 1751893415117, "results": "38", "hashOfConfig": "21"}, {"size": 7234, "mtime": 1751887803474, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1dzgleq", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\page.tsx", ["97", "98", "99", "100", "101", "102", "103", "104", "105", "106", "107", "108"], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\work\\page.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\compositions\\OrganicComposition.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\layout\\PageWrapper.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\performance\\PerformanceMonitor.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\AnimatedShapes.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Circle.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\HalfCircle.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Rectangle.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\RoundedShapes.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\shapes\\Triangle.tsx", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\hooks\\useScrollAnimation.ts", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\Projects\\navhaus\\src\\components\\ui\\LoadTimeDisplay.tsx", [], [], {"ruleId": "109", "severity": 1, "message": "110", "line": 439, "column": 29, "nodeType": "111", "endLine": 439, "endColumn": 248}, {"ruleId": "109", "severity": 1, "message": "110", "line": 453, "column": 29, "nodeType": "111", "endLine": 453, "endColumn": 238}, {"ruleId": "109", "severity": 1, "message": "110", "line": 467, "column": 29, "nodeType": "111", "endLine": 467, "endColumn": 236}, {"ruleId": "109", "severity": 1, "message": "110", "line": 481, "column": 29, "nodeType": "111", "endLine": 481, "endColumn": 248}, {"ruleId": "109", "severity": 1, "message": "110", "line": 505, "column": 29, "nodeType": "111", "endLine": 505, "endColumn": 250}, {"ruleId": "109", "severity": 1, "message": "110", "line": 519, "column": 29, "nodeType": "111", "endLine": 519, "endColumn": 250}, {"ruleId": "109", "severity": 1, "message": "110", "line": 533, "column": 29, "nodeType": "111", "endLine": 533, "endColumn": 244}, {"ruleId": "109", "severity": 1, "message": "110", "line": 547, "column": 29, "nodeType": "111", "endLine": 547, "endColumn": 238}, {"ruleId": "109", "severity": 1, "message": "110", "line": 575, "column": 29, "nodeType": "111", "endLine": 575, "endColumn": 240}, {"ruleId": "109", "severity": 1, "message": "110", "line": 589, "column": 29, "nodeType": "111", "endLine": 589, "endColumn": 241}, {"ruleId": "109", "severity": 1, "message": "110", "line": 613, "column": 29, "nodeType": "111", "endLine": 613, "endColumn": 236}, {"ruleId": "109", "severity": 1, "message": "110", "line": 627, "column": 29, "nodeType": "111", "endLine": 627, "endColumn": 240}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement"]