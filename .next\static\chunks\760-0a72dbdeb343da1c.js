"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[760],{4890:function(e,t,r){r.r(t),r.d(t,{default:function(){return o}});var l=r(7437),a=r(6691),n=r.n(a),s=r(1396),i=r.n(s),c=r(2783);function o(){return(0,l.jsxs)("footer",{className:"relative w-full bg-brand-background text-bauhaus-black overflow-hidden",children:[(0,l.jsx)("div",{className:"absolute inset-0 opacity-20",children:(0,l.jsx)(c.Animated<PERSON>oftGrid,{className:"w-full h-full text-black",opacity:"default",animationPreset:"drift",animationIndex:200})}),(0,l.jsx)("div",{className:"relative z-10 px-6 md:px-12 lg:px-24 py-16 md:py-20",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-12 gap-12 lg:gap-16 mb-16",children:[(0,l.jsxs)("div",{className:"lg:col-span-5 space-y-6",children:[(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(n(),{src:"/images/logo.png",alt:"Navhaus",width:140,height:45,className:"h-10 w-auto"}),(0,l.jsx)("div",{className:"absolute -top-2 -right-8",children:(0,l.jsx)(c.AnimatedSoftCircle,{size:"sm",color:"red",className:"w-4 h-4",animationPreset:"gentle",animationIndex:201})})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-xl font-bold text-bauhaus-black",children:"What matters, made real."}),(0,l.jsx)("p",{className:"text-gray-700 leading-relaxed max-w-sm",children:"We build bold, efficient, and meaningful digital experiences. Nothing more, nothing less."})]})]}),(0,l.jsx)("div",{className:"lg:col-span-3 lg:mt-16",children:(0,l.jsxs)("nav",{className:"space-y-4",children:[(0,l.jsx)(i(),{href:"/",className:"block text-bauhaus-black hover:text-bauhaus-red transition-colors duration-200 font-medium",children:"Home"}),(0,l.jsx)(i(),{href:"/about",className:"block text-bauhaus-black hover:text-bauhaus-red transition-colors duration-200 font-medium",children:"About"}),(0,l.jsx)(i(),{href:"/work",className:"block text-bauhaus-black hover:text-bauhaus-red transition-colors duration-200 font-medium",children:"Work"}),(0,l.jsx)(i(),{href:"/contact",className:"block text-bauhaus-black hover:text-bauhaus-red transition-colors duration-200 font-medium",children:"Contact"})]})}),(0,l.jsxs)("div",{className:"lg:col-span-4 space-y-8 lg:mt-16",children:[(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)("p",{className:"text-bauhaus-black font-medium",children:"Ready to build something?"}),(0,l.jsx)(i(),{href:"/contact",className:"inline-block px-4 py-2 border-2 border-bauhaus-black bg-transparent text-bauhaus-black font-bold uppercase tracking-wide hover:bg-bauhaus-black hover:text-brand-background transition-colors duration-200 rounded-xl text-sm",children:"Start here"})]}),(0,l.jsxs)("div",{className:"flex flex-col gap-2 text-sm text-gray-600",children:[(0,l.jsxs)("div",{className:"flex space-x-1",children:[(0,l.jsx)("div",{className:"w-2 h-2 bg-bauhaus-red rounded-full"}),(0,l.jsx)("div",{className:"w-2 h-2 bg-bauhaus-yellow rounded-full"}),(0,l.jsx)("div",{className:"w-2 h-2 bg-bauhaus-blue rounded-full"})]}),(0,l.jsx)("span",{children:"Usually responds within 24 hours"})]})]})]}),(0,l.jsxs)("div",{className:"relative py-8 mb-12",children:[(0,l.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,l.jsx)("div",{className:"w-full h-px bg-gray-800"})}),(0,l.jsxs)("div",{className:"relative flex justify-center space-x-8",children:[(0,l.jsx)(c.AnimatedRoundedRectangle,{width:"lg",height:"sm",color:"red",className:"w-16 h-4",animationPreset:"flowing",animationIndex:205}),(0,l.jsx)(c.AnimatedSoftCircle,{size:"md",color:"yellow",className:"w-8 h-8",animationPreset:"pulse",animationIndex:206}),(0,l.jsx)(c.AnimatedTriangle,{size:"md",color:"blue",direction:"up",className:"w-8 h-8",animationPreset:"dynamic",animationIndex:207})]})]}),(0,l.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,l.jsxs)("div",{className:"text-sm text-gray-600",children:["\xa9 ",new Date().getFullYear()," Navhaus. All rights reserved."]}),(0,l.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-600",children:[(0,l.jsx)("span",{children:"Built with intention"}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)("div",{className:"w-2 h-2 bg-bauhaus-red rounded-full"}),(0,l.jsx)("div",{className:"w-2 h-2 bg-bauhaus-yellow rounded-full"}),(0,l.jsx)("div",{className:"w-2 h-2 bg-bauhaus-blue rounded-full"})]})]})]})]})}),(0,l.jsx)("div",{className:"absolute top-16 left-16 opacity-15",children:(0,l.jsx)(c.AnimatedBlob,{color:"red",className:"w-24 h-24",animationPreset:"drift",animationIndex:208})}),(0,l.jsx)("div",{className:"absolute bottom-20 right-20 opacity-10",children:(0,l.jsx)(c.AnimatedQuarterCircle,{color:"blue",corner:"top-left",className:"w-32 h-32",animationPreset:"gentle",animationIndex:209})}),(0,l.jsx)("div",{className:"absolute top-1/2 right-8 opacity-8",children:(0,l.jsx)(c.AnimatedRoundedRectangle,{width:"lg",height:"xl",color:"yellow",className:"w-8 h-24",animationPreset:"float",animationIndex:210})}),(0,l.jsx)("div",{className:"absolute bottom-8 left-1/4 opacity-12",children:(0,l.jsx)(c.AnimatedSoftCircle,{size:"lg",color:"red",className:"w-16 h-16",animationPreset:"energetic",animationIndex:211})}),(0,l.jsx)("div",{className:"absolute top-20 right-1/3 opacity-8",children:(0,l.jsx)(c.AnimatedTriangle,{size:"lg",color:"yellow",direction:"up",className:"w-12 h-12",animationPreset:"pulse",animationIndex:212})})]})}},2767:function(e,t,r){r.r(t),r.d(t,{default:function(){return d}});var l=r(7437),a=r(2265),n=r(1396),s=r.n(n),i=r(6691),c=r.n(i),o=r(4033);function d(){let[e,t]=(0,a.useState)(!1),r=(0,o.usePathname)(),n=[{href:"/",label:"Home"},{href:"/about",label:"About"},{href:"/contact",label:"Contact"}];return(0,l.jsx)("header",{className:"w-full py-6 px-6 md:px-12 lg:px-24",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,l.jsxs)("nav",{className:"flex justify-between items-center",children:[(0,l.jsx)(s(),{href:"/",className:"flex items-center",children:(0,l.jsx)(c(),{src:"/images/logo.png",alt:"Navhaus",width:120,height:40,className:"h-8 w-auto lg:-mt-[15px]"})}),(0,l.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[n.map(e=>(0,l.jsx)(s(),{href:e.href,className:"font-medium uppercase tracking-wide transition-colors duration-200 ".concat(r===e.href?"text-bauhaus-red":"text-bauhaus-black hover:text-bauhaus-red"),children:e.label},e.href)),(0,l.jsx)(s(),{href:"/contact",className:"btn-primary ml-8",children:"Start Project"})]}),(0,l.jsx)("button",{className:"md:hidden text-bauhaus-black",onClick:()=>t(!e),children:(0,l.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e?(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})]}),e&&(0,l.jsx)("div",{className:"md:hidden mt-6 py-6 border-t border-bauhaus-black",children:(0,l.jsxs)("div",{className:"flex flex-col space-y-4",children:[n.map(e=>(0,l.jsx)(s(),{href:e.href,className:"font-medium uppercase tracking-wide transition-colors duration-200 ".concat(r===e.href?"text-bauhaus-red":"text-bauhaus-black hover:text-bauhaus-red"),onClick:()=>t(!1),children:e.label},e.href)),(0,l.jsx)(s(),{href:"/contact",className:"btn-primary inline-block mt-4",onClick:()=>t(!1),children:"Start Project"})]})})]})})}},2783:function(e,t,r){r.r(t),r.d(t,{AnimatedBlob:function(){return M},AnimatedCircle:function(){return w},AnimatedHalfCircle:function(){return k},AnimatedPill:function(){return C},AnimatedQuarterCircle:function(){return P},AnimatedRectangle:function(){return y},AnimatedRoundedRect:function(){return A},AnimatedRoundedRectangle:function(){return N},AnimatedSoftCircle:function(){return j},AnimatedSoftGrid:function(){return S},AnimatedTriangle:function(){return v}});var l=r(7437),a=r(2265);function n(e){let[t,r]=(0,a.useState)(0),l=(0,a.useRef)(null),[n,s]=(0,a.useState)({x:0,y:0}),[i,c]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let t=setTimeout(()=>{let t=e.randomSeed||Math.random(),r=43758.5453*Math.sin(45.164*t)%1*Math.PI*2,l=.8+.4*Math.abs(43758.5453*Math.sin(12.9898*t)%1);s({x:Math.cos(r)*l,y:Math.sin(r)*l}),c(!0)},50);return()=>clearTimeout(t)},[e.randomSeed]),(0,a.useEffect)(()=>{if(!i)return;let e=()=>{r(window.scrollY)};return window.addEventListener("scroll",e,{passive:!0}),()=>{window.removeEventListener("scroll",e)}},[i]),{ref:l,style:{transform:(()=>{var r;if(!l.current||!i)return"translate3d(0, 0, 0)";let a=l.current.getBoundingClientRect(),s=a.top+t,c=(r=Math.max(0,Math.min(1,(t-s+window.innerHeight)/(window.innerHeight+a.height))))<.5?2*r*r:-1+(4-2*r)*r,o=0,d=0;return("x"===e.direction||"both"===e.direction)&&(o=c*e.intensity*n.x*e.speed),("y"===e.direction||"both"===e.direction)&&(d=c*e.intensity*n.y*e.speed),"translate3d(".concat(o,"px, ").concat(d,"px, 0)")})(),transition:"transform 0.1s ease-out",willChange:"transform"}}}let s={subtle:{direction:"both",intensity:20,speed:.6},gentle:{direction:"both",intensity:30,speed:.8},dynamic:{direction:"both",intensity:45,speed:1.2},flowing:{direction:"both",intensity:35,speed:.9},energetic:{direction:"both",intensity:50,speed:1.4},drift:{direction:"both",intensity:25,speed:.5},pulse:{direction:"both",intensity:35,speed:1.1},float:{direction:"both",intensity:15,speed:.4},horizontal:{direction:"x",intensity:30,speed:.8}};var i=r(5057),c=r(8548);let o={sm:"w-0 h-0 border-l-[16px] border-r-[16px] border-b-[28px]",md:"w-0 h-0 border-l-[24px] border-r-[24px] border-b-[42px]",lg:"w-0 h-0 border-l-[32px] border-r-[32px] border-b-[56px]",xl:"w-0 h-0 border-l-[48px] border-r-[48px] border-b-[84px]"},d=(e,t,r)=>{let l=o[e];switch(r){case"up":default:return"".concat(l," border-l-transparent border-r-transparent");case"down":return l.replace("border-b-","border-t-")+" border-l-transparent border-r-transparent";case"left":return l.replace("border-l-","border-r-").replace("border-r-","border-t-").replace("border-b-","border-l-")+" border-t-transparent border-b-transparent";case"right":return l.replace("border-r-","border-l-").replace("border-l-","border-t-").replace("border-b-","border-r-")+" border-t-transparent border-b-transparent"}};function u(e){let{size:t="md",color:r="yellow",direction:a="up",className:n=""}=e,s=d(t,r,a);return(0,l.jsx)("div",{className:"".concat(s," ").concat(n),style:{borderBottomColor:"up"===a?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent",borderTopColor:"down"===a?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent",borderLeftColor:"right"===a?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent",borderRightColor:"left"===a?"red"===r?"#e94436":"yellow"===r?"#ffc527":"blue"===r?"#434897":"black"===r?"#000000":"#ffffff":"transparent"}})}let b={sm:"w-8 h-4",md:"w-16 h-8",lg:"w-24 h-12",xl:"w-32 h-16"},h={red:"bg-bauhaus-red",yellow:"bg-bauhaus-yellow",blue:"bg-bauhaus-blue",black:"bg-bauhaus-black",white:"bg-bauhaus-white border border-bauhaus-black"},f={top:"rounded-t-full",bottom:"rounded-b-full",left:"rounded-l-full",right:"rounded-r-full"},m=e=>{switch(e){case"left":case"right":return"w-4 h-8 md:w-8 md:h-16 lg:w-12 lg:h-24 xl:w-16 xl:h-32";default:return""}};function x(e){let{size:t="md",color:r="red",direction:a="top",className:n=""}=e,s=m(a)||b[t];return(0,l.jsx)("div",{className:"".concat(s," ").concat(h[r]," ").concat(f[a]," ").concat(n)})}var g=r(1116);let p=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=0,l="".concat(e,"-").concat(t);for(let e=0;e<l.length;e++)r=(r<<5)-r+l.charCodeAt(e),r&=r;return Math.abs(r)/2147483647};function w(e){let{animationPreset:t="gentle",animationIndex:r=0,...a}=e,c=n({...s[t],randomSeed:p("circle",r)});return(0,l.jsx)("div",{ref:c.ref,className:"h-full",style:c.style,children:(0,l.jsx)(i.Z,{...a})})}function j(e){let{animationPreset:t="subtle",animationIndex:r=0,...a}=e,c=n({...s[t],randomSeed:p("soft-circle",r)});return(0,l.jsx)("div",{ref:c.ref,className:"h-full",style:c.style,children:(0,l.jsx)(i.H,{...a})})}function y(e){let{animationPreset:t="gentle",animationIndex:r=0,...a}=e,i=n({...s[t],randomSeed:p("rectangle",r)});return(0,l.jsx)("div",{ref:i.ref,className:"h-full",style:i.style,children:(0,l.jsx)(c.ZP,{...a})})}function N(e){let{animationPreset:t="gentle",animationIndex:r=0,...a}=e,i=n({...s[t],randomSeed:p("rounded-rectangle",r)});return(0,l.jsx)("div",{ref:i.ref,className:"h-full",style:i.style,children:(0,l.jsx)(c.c9,{...a})})}function v(e){let{animationPreset:t="dynamic",animationIndex:r=0,...a}=e,i=n({...s[t],randomSeed:p("triangle",r)});return(0,l.jsx)("div",{ref:i.ref,className:"h-full",style:i.style,children:(0,l.jsx)(u,{...a})})}function k(e){let{animationPreset:t="gentle",animationIndex:r=0,...a}=e,i=n({...s[t],randomSeed:p("half-circle",r)});return(0,l.jsx)("div",{ref:i.ref,className:"h-full",style:i.style,children:(0,l.jsx)(x,{...a})})}function S(e){let{animationPreset:t="subtle",animationIndex:r=0,opacity:a="default",...i}=e,c=n({...s[t],randomSeed:p("soft-grid",r)});return(0,l.jsx)("div",{ref:c.ref,className:"h-full",style:c.style,children:(0,l.jsx)(g.Xg,{opacity:a,...i})})}function A(e){let{animationPreset:t="gentle",animationIndex:r=0,...a}=e,i=n({...s[t],randomSeed:p("rounded-rect",r)});return(0,l.jsx)("div",{ref:i.ref,className:"h-full",style:i.style,children:(0,l.jsx)(g.uM,{...a})})}function C(e){let{animationPreset:t="horizontal",animationIndex:r=0,...a}=e,i=n({...s[t],randomSeed:p("pill",r)});return(0,l.jsx)("div",{ref:i.ref,className:"h-full",style:i.style,children:(0,l.jsx)(g.DR,{...a})})}function M(e){let{animationPreset:t="dynamic",animationIndex:r=0,...a}=e,i=n({...s[t],randomSeed:p("blob",r)});return(0,l.jsx)("div",{ref:i.ref,className:"h-full",style:i.style,children:(0,l.jsx)(g.t6,{...a})})}function P(e){let{animationPreset:t="gentle",animationIndex:r=0,...a}=e,i=n({...s[t],randomSeed:p("quarter-circle",r)});return(0,l.jsx)("div",{ref:i.ref,className:"h-full",style:i.style,children:(0,l.jsx)(g.kw,{...a})})}},5057:function(e,t,r){r.d(t,{H:function(){return i},Z:function(){return s}});var l=r(7437);let a={sm:"w-8 h-8",md:"w-16 h-16",lg:"w-24 h-24",xl:"w-32 h-32"},n={red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black",white:"bg-white border border-black"};function s(e){let{size:t="md",color:r="red",className:s=""}=e;return(0,l.jsx)("div",{className:"rounded-full ".concat(a[t]," ").concat(n[r]," ").concat(s)})}function i(e){let{size:t="md",color:r="red",className:s=""}=e;return(0,l.jsx)("div",{className:"rounded-full ".concat(a[t]," ").concat(n[r]," ").concat(s),style:{filter:"blur(0.5px)"}})}},8548:function(e,t,r){r.d(t,{ZP:function(){return i},c9:function(){return c}});var l=r(7437);let a={sm:"w-12",md:"w-24",lg:"w-32",xl:"w-48"},n={sm:"h-8",md:"h-16",lg:"h-24",xl:"h-32"},s={red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black",white:"bg-white border border-black"};function i(e){let{width:t="md",height:r="md",color:i="blue",className:c=""}=e;return(0,l.jsx)("div",{className:"".concat(a[t]," ").concat(n[r]," ").concat(s[i]," ").concat(c)})}function c(e){let{width:t="md",height:r="md",color:i="blue",className:c=""}=e;return(0,l.jsx)("div",{className:"rounded-3xl ".concat(a[t]," ").concat(n[r]," ").concat(s[i]," ").concat(c)})}},1116:function(e,t,r){r.d(t,{B$:function(){return i},DR:function(){return n},Xg:function(){return o},kw:function(){return c},t6:function(){return s},uM:function(){return a}});var l=r(7437);function a(e){let{className:t="",color:r="blue"}=e;return(0,l.jsx)("div",{className:"".concat({red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[r]," rounded-3xl ").concat(t)})}function n(e){let{className:t="",color:r="yellow"}=e;return(0,l.jsx)("div",{className:"".concat({red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[r]," rounded-full ").concat(t)})}function s(e){let{className:t="",color:r="red"}=e;return(0,l.jsx)("div",{className:"".concat({red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[r]," ").concat(t),style:{borderRadius:"60% 40% 30% 70% / 60% 30% 70% 40%"}})}function i(e){let{className:t="",color:r="blue",direction:a="right"}=e;return(0,l.jsx)("div",{className:"".concat({red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[r]," ").concat({right:"rounded-l-full",left:"rounded-r-full",top:"rounded-b-full",bottom:"rounded-t-full"}[a]," ").concat(t)})}function c(e){let{className:t="",color:r="yellow",corner:a="top-left"}=e;return(0,l.jsx)("div",{className:"".concat({red:"bg-brand-red",yellow:"bg-brand-yellow",blue:"bg-brand-blue",black:"bg-black"}[r]," ").concat({"top-left":"rounded-br-full","top-right":"rounded-bl-full","bottom-left":"rounded-tr-full","bottom-right":"rounded-tl-full"}[a]," ").concat(t)})}function o(e){let{className:t="",opacity:r="default"}=e;return(0,l.jsx)("div",{className:"absolute inset-0 ".concat("hero"===r?"opacity-40":"opacity-20"," ").concat(t),children:(0,l.jsxs)("svg",{width:"100%",height:"100%",className:"w-full h-full",children:[(0,l.jsx)("defs",{children:(0,l.jsx)("pattern",{id:"grid",width:"40",height:"40",patternUnits:"userSpaceOnUse",children:(0,l.jsx)("path",{d:"M 40 0 L 0 0 0 40",fill:"none",stroke:"currentColor",strokeWidth:"1",opacity:"hero"===r?"0.5":"0.3"})})}),(0,l.jsx)("rect",{width:"100%",height:"100%",fill:"url(#grid)"})]})})}}}]);