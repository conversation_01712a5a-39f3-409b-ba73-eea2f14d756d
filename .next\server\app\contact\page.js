(()=>{var e={};e.id=327,e.ids=[327],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},67:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var t=s(482),l=s(9108),i=s(2563),n=s.n(i),r=s(8300),o={};for(let e in r)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>r[e]);s.d(a,o);let c=["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1215)),"C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\contact\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,7843)),"C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\contact\\page.tsx"],m="/contact/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5951:(e,a,s)=>{Promise.resolve().then(s.bind(s,5893))},5893:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>c});var t=s(2295),l=s(3729),i=s(9705),n=s(5590);function r({children:e,className:a=""}){return(0,t.jsxs)("div",{className:"min-h-screen flex flex-col",children:[t.jsx(i.default,{}),t.jsx("main",{className:`flex-1 ${a}`,children:e}),t.jsx(n.default,{})]})}var o=s(2859);function c(){let[e,a]=(0,l.useState)({name:"",email:"",message:""}),[s,i]=(0,l.useState)(!1),[n,c]=(0,l.useState)("idle"),d=s=>{a({...e,[s.target.name]:s.target.value})},m=async s=>{s.preventDefault(),i(!0),c("idle");try{(await fetch("https://formspree.io/f/xzzgqnvq",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,email:e.email,message:e.message,_replyto:e.email})})).ok?(c("success"),a({name:"",email:"",message:""})):c("error")}catch(e){console.error("Form submission error:",e),c("error")}finally{i(!1)}};return(0,t.jsxs)(r,{children:[(0,t.jsxs)("section",{className:"relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden",children:[(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[t.jsx("div",{className:"absolute inset-0 opacity-20",children:t.jsx(o.AnimatedSoftGrid,{className:"w-full h-full text-black",opacity:"hero",animationPreset:"subtle",animationIndex:0})}),(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-6 mb-16",children:[(0,t.jsxs)("div",{className:"col-span-12 lg:col-span-8",children:[t.jsx("h1",{className:"text-5xl md:text-6xl font-bold leading-none mb-6",children:"Have something worth building?"}),t.jsx("p",{className:"text-xl leading-relaxed text-gray-700 max-w-2xl",children:"Tell us about your project and we'll get back to you within 24 hours."})]}),t.jsx("div",{className:"col-span-12 lg:col-span-4 flex items-center justify-end",children:(0,t.jsxs)("div",{className:"relative",children:[t.jsx(o.AnimatedSoftCircle,{size:"xl",color:"red",className:"w-24 h-24",animationPreset:"gentle",animationIndex:1}),t.jsx("div",{className:"absolute -bottom-4 -right-4",children:t.jsx(o.AnimatedRoundedRectangle,{width:"lg",height:"sm",color:"yellow",className:"w-16 h-4",animationPreset:"drift",animationIndex:2})})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-6",children:[t.jsx("div",{className:"col-span-12 lg:col-span-8",children:(0,t.jsxs)("div",{className:"bg-brand-background border-3 border-bauhaus-black py-8 md:py-12 rounded-3xl relative overflow-hidden",children:[t.jsx("div",{className:"absolute inset-0 opacity-40",children:t.jsx(o.AnimatedSoftGrid,{className:"w-full h-full text-black",opacity:"default",animationPreset:"subtle",animationIndex:3})}),t.jsx("div",{className:"absolute top-4 right-4",children:t.jsx(o.AnimatedSoftCircle,{size:"sm",color:"red",className:"w-6 h-6",animationPreset:"gentle",animationIndex:4})}),(0,t.jsxs)("form",{onSubmit:m,className:"space-y-6 relative z-10",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("label",{htmlFor:"name",className:"block text-sm font-bold uppercase tracking-wide text-bauhaus-black",children:"Name"}),t.jsx("input",{type:"text",id:"name",name:"name",value:e.name,onChange:d,required:!0,className:"w-full px-6 py-4 bg-bauhaus-white text-bauhaus-black rounded-2xl focus:outline-none focus:ring-2 focus:ring-bauhaus-red transition-all duration-200 border-2 border-bauhaus-black focus:border-bauhaus-red"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("label",{htmlFor:"email",className:"block text-sm font-bold uppercase tracking-wide text-bauhaus-black",children:"Email"}),t.jsx("input",{type:"email",id:"email",name:"email",value:e.email,onChange:d,required:!0,className:"w-full px-6 py-4 bg-bauhaus-white text-bauhaus-black rounded-2xl focus:outline-none focus:ring-2 focus:ring-bauhaus-red transition-all duration-200 border-2 border-bauhaus-black focus:border-bauhaus-red"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("label",{htmlFor:"message",className:"block text-sm font-bold uppercase tracking-wide text-bauhaus-black",children:"Message"}),t.jsx("textarea",{id:"message",name:"message",value:e.message,onChange:d,required:!0,rows:6,className:"w-full px-6 py-4 bg-bauhaus-white text-bauhaus-black rounded-2xl focus:outline-none focus:ring-2 focus:ring-bauhaus-red transition-all duration-200 resize-none border-2 border-bauhaus-black focus:border-bauhaus-red",placeholder:"Tell us about your project, timeline, and goals..."})]}),(0,t.jsxs)("div",{className:"pt-4",children:[t.jsx("button",{type:"submit",disabled:s,className:"btn-red text-lg px-8 py-4 disabled:opacity-50 disabled:cursor-not-allowed",children:s?"Sending...":"Start here"}),"success"===n&&t.jsx("div",{className:"mt-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-2xl",children:"Thanks! We'll get back to you within 24 hours."}),"error"===n&&t.jsx("div",{className:"mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-2xl",children:"Something went wrong. Please try again or email us <NAME_EMAIL>"})]})]})]})}),t.jsx("div",{className:"col-span-12 lg:col-span-4",children:(0,t.jsxs)("div",{className:"bg-bauhaus-yellow text-bauhaus-black p-8 rounded-3xl relative overflow-hidden h-full flex flex-col justify-center",children:[t.jsx("div",{className:"absolute bottom-4 right-4",children:t.jsx(o.AnimatedRoundedRectangle,{width:"sm",height:"sm",color:"black",className:"w-4 h-4",animationPreset:"gentle",animationIndex:5})}),(0,t.jsxs)("div",{className:"relative z-10 text-center",children:[t.jsx("div",{className:"text-6xl font-bold mb-4",children:"24h"}),t.jsx("p",{className:"text-xl leading-tight",children:"Response time for all inquiries"})]})]})})]})]}),t.jsx("div",{className:"absolute top-16 left-16 opacity-10",children:t.jsx(o.AnimatedBlob,{color:"blue",className:"w-32 h-32",animationPreset:"drift",animationIndex:9})}),t.jsx("div",{className:"absolute bottom-20 right-20 opacity-15",children:t.jsx(o.AnimatedSoftCircle,{size:"lg",color:"yellow",className:"w-20 h-20",animationPreset:"gentle",animationIndex:10})})]}),(0,t.jsxs)("section",{className:"relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-bauhaus-black text-bauhaus-white overflow-hidden",children:[t.jsx("div",{className:"max-w-7xl mx-auto",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center",children:[(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[t.jsx("h2",{className:"text-4xl md:text-5xl font-bold leading-none",children:"Or reach out directly"}),t.jsx("p",{className:"text-xl leading-relaxed opacity-90 max-w-lg",children:"We like projects that are sharp, fast, and meaningful. If that's what you've got, we should talk."})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-bauhaus-blue text-bauhaus-white p-6 rounded-3xl relative overflow-hidden",children:[t.jsx("div",{className:"absolute bottom-2 right-2",children:t.jsx(o.AnimatedSoftCircle,{size:"sm",color:"yellow",className:"w-4 h-4",animationPreset:"gentle",animationIndex:3})}),(0,t.jsxs)("div",{className:"relative z-10",children:[t.jsx("h3",{className:"font-bold mb-2",children:"Email us directly"}),t.jsx("p",{className:"text-lg",children:"<EMAIL>"})]})]}),(0,t.jsxs)("div",{className:"bg-bauhaus-yellow text-bauhaus-black p-6 rounded-3xl relative overflow-hidden",children:[t.jsx("div",{className:"absolute top-2 right-2",children:t.jsx(o.AnimatedRoundedRectangle,{width:"sm",height:"sm",color:"blue",className:"w-4 h-4",animationPreset:"gentle",animationIndex:4})}),(0,t.jsxs)("div",{className:"relative z-10",children:[t.jsx("h3",{className:"font-bold mb-2",children:"Schedule a call"}),t.jsx("p",{className:"text-lg",children:"Book 30 minutes to discuss your project"})]})]})]})]}),(0,t.jsxs)("div",{className:"relative h-96 lg:h-[500px]",children:[t.jsx("div",{className:"absolute inset-0 opacity-20",children:t.jsx(o.AnimatedSoftGrid,{className:"w-full h-full text-white",opacity:"default",animationPreset:"subtle",animationIndex:5})}),t.jsx("div",{className:"absolute top-16 right-12",children:t.jsx(o.AnimatedSoftCircle,{size:"xl",color:"red",className:"w-24 h-24",animationPreset:"gentle",animationIndex:6})}),t.jsx("div",{className:"absolute bottom-20 left-8",children:t.jsx(o.AnimatedBlob,{color:"blue",className:"w-32 h-32",animationPreset:"flowing",animationIndex:7})}),t.jsx("div",{className:"absolute top-32 left-16",children:t.jsx(o.AnimatedPill,{color:"yellow",className:"w-20 h-8",animationPreset:"drift",animationIndex:8})}),t.jsx("div",{className:"absolute bottom-32 right-20",children:t.jsx(o.AnimatedRoundedRectangle,{width:"lg",height:"md",color:"white",className:"w-16 h-12",animationPreset:"gentle",animationIndex:9})}),t.jsx("div",{className:"absolute top-8 left-32 opacity-80",children:t.jsx(o.AnimatedSoftCircle,{size:"sm",color:"yellow",className:"w-6 h-6",animationPreset:"energetic",animationIndex:10})}),t.jsx("div",{className:"absolute bottom-8 right-8 opacity-70",children:t.jsx(o.AnimatedPill,{color:"red",className:"w-12 h-4",animationPreset:"float",animationIndex:11})})]})]})}),t.jsx("div",{className:"absolute top-16 left-16 opacity-10",children:t.jsx(o.AnimatedBlob,{color:"yellow",className:"w-24 h-24",animationPreset:"drift",animationIndex:12})}),t.jsx("div",{className:"absolute bottom-20 right-20 opacity-8",children:t.jsx(o.AnimatedSoftCircle,{size:"lg",color:"red",className:"w-20 h-20",animationPreset:"gentle",animationIndex:13})})]})]})}},1215:(e,a,s)=>{"use strict";s.r(a),s.d(a,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});let t=(0,s(6843).createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\app\contact\page.tsx`),{__esModule:l,$$typeof:i}=t,n=t.default}};var a=require("../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),t=a.X(0,[638,644,30,366],()=>s(67));module.exports=t})();