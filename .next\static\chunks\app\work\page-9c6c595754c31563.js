(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[534],{7449:function(e,l,s){Promise.resolve().then(s.bind(s,8735)),Promise.resolve().then(s.bind(s,4890)),Promise.resolve().then(s.bind(s,2767))},8735:function(e,l,s){"use strict";s.r(l),s.d(l,{FeatureComposition:function(){return m},HeroComposition:function(){return u},MinimalComposition:function(){return h},StaticHeroComposition:function(){return r},default:function(){return d}});var t=s(7437),a=s(1116),o=s(5057),i=s(8548),c=s(2783),n=s(2265);function r(e){let{className:l=""}=e;return(0,t.jsxs)("div",{className:"relative ".concat(l),children:[(0,t.jsx)(a.Xg,{className:"text-black h-full",opacity:"hero"}),(0,t.jsx)("div",{className:"absolute top-0 right-0 w-64 h-64 opacity-80",children:(0,t.jsx)(a.B$,{color:"blue",direction:"right",className:"w-full h-full"})}),(0,t.jsx)("div",{className:"absolute bottom-0 left-0 w-48 h-48",children:(0,t.jsx)(a.kw,{color:"yellow",corner:"bottom-left",className:"w-full h-full"})}),(0,t.jsx)("div",{className:"absolute top-1/4 left-1/3 w-24 h-24 opacity-90",children:(0,t.jsx)(a.t6,{color:"red",className:"w-full h-full"})}),(0,t.jsx)("div",{className:"absolute top-1/2 right-1/4 w-20 h-16",children:(0,t.jsx)(o.H,{size:"lg",color:"yellow",className:"h-full"})}),(0,t.jsx)("div",{className:"absolute bottom-1/3 left-1/4 w-16 h-6",children:(0,t.jsx)(a.DR,{color:"black",className:"w-full h-full"})}),(0,t.jsx)("div",{className:"absolute top-3/4 right-1/3 w-12 h-12",children:(0,t.jsx)(a.uM,{color:"blue",className:"w-full h-full"})})]})}function u(e){let{className:l=""}=e,[s,a]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{let e=setTimeout(()=>{a(!0)},100);return()=>clearTimeout(e)},[]),s)?(0,t.jsxs)("div",{className:"relative ".concat(l),children:[(0,t.jsx)(c.AnimatedSoftGrid,{className:"text-black h-full",opacity:"hero",animationPreset:"subtle",animationIndex:0}),(0,t.jsx)("div",{className:"absolute top-0 right-0 w-64 h-64 opacity-80",children:(0,t.jsx)(c.AnimatedHalfCircle,{color:"blue",direction:"right",className:"w-full h-full",animationPreset:"gentle",animationIndex:1})}),(0,t.jsx)("div",{className:"absolute bottom-0 left-0 w-48 h-48",children:(0,t.jsx)(c.AnimatedQuarterCircle,{color:"yellow",corner:"bottom-left",className:"w-full h-full",animationPreset:"flowing",animationIndex:2})}),(0,t.jsx)("div",{className:"absolute top-1/4 left-1/3 w-24 h-24 opacity-90",children:(0,t.jsx)(c.AnimatedBlob,{color:"red",className:"w-full h-full",animationPreset:"dynamic",animationIndex:3})}),(0,t.jsx)("div",{className:"absolute top-1/2 right-1/4 w-20 h-16",children:(0,t.jsx)(c.AnimatedSoftCircle,{size:"lg",color:"yellow",className:"h-full",animationPreset:"energetic",animationIndex:4})}),(0,t.jsx)("div",{className:"absolute bottom-1/3 left-1/4 w-16 h-6",children:(0,t.jsx)(c.AnimatedPill,{color:"black",className:"w-full h-full",animationPreset:"horizontal",animationIndex:5})}),(0,t.jsx)("div",{className:"absolute top-3/4 right-1/3 w-12 h-12",children:(0,t.jsx)(c.AnimatedRoundedRect,{color:"blue",className:"w-full h-full",animationPreset:"pulse",animationIndex:6})})]}):(0,t.jsx)(r,{className:l})}function m(e){let{className:l=""}=e;return(0,t.jsxs)("div",{className:"relative ".concat(l),children:[(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-8",children:[(0,t.jsx)(o.H,{size:"xl",color:"blue"}),(0,t.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,t.jsx)(i.c9,{width:"lg",height:"xl",color:"yellow"}),(0,t.jsx)(o.H,{size:"lg",color:"black"})]}),(0,t.jsx)(a.kw,{color:"red",corner:"top-right",className:"w-32 h-32"})]}),(0,t.jsx)(a.B$,{color:"blue",direction:"left",className:"absolute -top-8 -right-8 w-24 h-24 opacity-60"})]})}function h(e){let{className:l=""}=e;return(0,t.jsxs)("div",{className:"relative ".concat(l),children:[(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-8 items-center",children:[(0,t.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,t.jsx)(o.H,{size:"md",color:"black"}),(0,t.jsx)(a.DR,{color:"yellow",className:"w-20 h-8"})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(a.uM,{color:"red",className:"w-24 h-24"}),(0,t.jsx)(o.H,{size:"sm",color:"black",className:"absolute -bottom-2 -right-2"})]}),(0,t.jsx)(a.t6,{color:"blue",className:"w-20 h-20"})]}),(0,t.jsx)("svg",{className:"absolute inset-0 w-full h-full pointer-events-none opacity-20",children:(0,t.jsx)("path",{d:"M 50 50 Q 150 100 250 50",stroke:"currentColor",strokeWidth:"1",fill:"none",className:"text-black"})})]})}function d(e){let{variant:l="hero",className:s=""}=e;switch(l){case"hero":default:return(0,t.jsx)(u,{className:s});case"feature":return(0,t.jsx)(m,{className:s});case"minimal":return(0,t.jsx)(h,{className:s})}}}},function(e){e.O(0,[99,760,971,938,744],function(){return e(e.s=7449)}),_N_E=e.O()}]);