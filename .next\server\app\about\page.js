(()=>{var e={};e.id=301,e.ids=[301],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},7915:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>d});var t=a(482),l=a(9108),i=a(2563),n=a.n(i),o=a(8300),r={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(r[e]=()=>o[e]);a.d(s,r);let d=["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,2304)),"C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\about\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,7843)),"C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\about\\page.tsx"],x="/about/page",m={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8070:(e,s,a)=>{Promise.resolve().then(a.t.bind(a,1476,23)),Promise.resolve().then(a.bind(a,5590)),Promise.resolve().then(a.bind(a,9705)),Promise.resolve().then(a.bind(a,2859))},8026:(e,s,a)=>{let{createProxy:t}=a(6843);e.exports=t("C:\\Users\\<USER>\\Projects\\navhaus\\node_modules\\next\\dist\\client\\link.js")},646:(e,s,a)=>{e.exports=a(8026)},2304:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});var t=a(5036),l=a(7621),i=a(646),n=a.n(i),o=a(8373);function r(){return(0,t.jsxs)(l.Z,{children:[(0,t.jsxs)("section",{className:"animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden",children:[(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[t.jsx("div",{className:"absolute inset-0 opacity-20",children:t.jsx(o.i6,{className:"w-full h-full text-black",opacity:"hero",animationPreset:"subtle",animationIndex:0})}),(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-6 relative z-10",children:[t.jsx("div",{className:"col-span-12 lg:col-span-8",children:(0,t.jsxs)("div",{className:"relative",children:[t.jsx("div",{className:"absolute -top-4 -left-4",children:t.jsx(o.IJ,{size:"lg",color:"red",className:"w-16 h-16",animationPreset:"gentle",animationIndex:1})}),(0,t.jsxs)("h1",{className:"text-6xl md:text-7xl lg:text-8xl font-bold leading-none mb-8 relative z-10",children:["WHAT",t.jsx("br",{}),"WE ARE"]})]})}),t.jsx("div",{className:"col-span-12 lg:col-span-4 flex items-start justify-end",children:t.jsx(o.U9,{width:"xl",height:"xl",color:"yellow",className:"w-32 h-24",animationPreset:"gentle",animationIndex:2})}),t.jsx("div",{className:"col-span-12 lg:col-span-5",children:(0,t.jsxs)("div",{className:"bg-bauhaus-black text-brand-background p-8 rounded-3xl relative overflow-hidden",children:[t.jsx("div",{className:"absolute inset-0 opacity-30",children:t.jsx(o.i6,{className:"w-full h-full text-brand-background",opacity:"default",animationPreset:"drift",animationIndex:3})}),t.jsx("div",{className:"absolute top-4 right-4",children:t.jsx(o.IJ,{size:"sm",color:"red",className:"w-6 h-6",animationPreset:"gentle",animationIndex:4})}),t.jsx("p",{className:"text-lg font-medium leading-tight relative z-10",children:"Navhaus is a small, senior team of designers and developers."})]})}),t.jsx("div",{className:"col-span-12 lg:col-span-2",children:t.jsx("div",{className:"h-full bg-bauhaus-blue rounded-3xl relative overflow-hidden",children:t.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",children:t.jsx(o._V,{size:"md",color:"white",direction:"up",className:"w-8 h-8",animationPreset:"pulse",animationIndex:5})})})}),t.jsx("div",{className:"col-span-12 lg:col-span-5",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-brand-background border-3 border-bauhaus-black p-6 rounded-3xl relative overflow-hidden",children:[t.jsx("div",{className:"absolute inset-0 opacity-40",children:t.jsx(o.i6,{className:"w-full h-full text-black",opacity:"default",animationPreset:"subtle",animationIndex:6})}),t.jsx("p",{className:"text-lg leading-tight relative z-10",children:"We build custom digital experiences. Fast, focused, and built to last."})]}),(0,t.jsxs)("div",{className:"bg-bauhaus-yellow text-bauhaus-black p-6 rounded-3xl relative overflow-hidden",children:[t.jsx("div",{className:"absolute bottom-2 right-2",children:t.jsx(o.U9,{width:"sm",height:"sm",color:"black",className:"w-4 h-4",animationPreset:"gentle",animationIndex:7})}),t.jsx("p",{className:"font-bold text-lg relative z-10",children:"No fluff. No bloat. Just what matters, made real."})]})]})})]})]}),t.jsx("div",{className:"absolute top-20 right-20 opacity-30",children:t.jsx(o.U9,{width:"sm",height:"xl",color:"blue",className:"w-8 h-40",animationPreset:"drift",animationIndex:8})}),t.jsx("div",{className:"absolute bottom-20 left-20 opacity-40",children:t.jsx(o.IJ,{size:"xl",color:"red",className:"w-24 h-24",animationPreset:"gentle",animationIndex:9})})]}),(0,t.jsxs)("section",{className:"animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background overflow-hidden",children:[(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-6 mb-16",children:[t.jsx("div",{className:"col-span-12 lg:col-span-6",children:(0,t.jsxs)("h2",{className:"text-5xl md:text-6xl font-bold leading-none",children:["WHAT WE",t.jsx("br",{}),t.jsx("span",{className:"text-bauhaus-red",children:"BELIEVE"})]})}),t.jsx("div",{className:"col-span-12 lg:col-span-6 flex items-end",children:t.jsx(o.U9,{width:"xl",height:"sm",color:"black",className:"w-full h-4",animationPreset:"gentle",animationIndex:11})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-6",children:[t.jsx("div",{className:"col-span-12 lg:col-span-8",children:(0,t.jsxs)("div",{className:"bg-bauhaus-black text-brand-background p-8 md:p-12 rounded-3xl relative overflow-hidden",children:[t.jsx("div",{className:"absolute inset-0 opacity-30",children:t.jsx(o.i6,{className:"w-full h-full text-brand-background",opacity:"default",animationPreset:"drift",animationIndex:12})}),t.jsx("div",{className:"absolute top-4 left-4",children:t.jsx(o.U9,{width:"xl",height:"sm",color:"red",className:"w-16 h-2",animationPreset:"gentle",animationIndex:13})}),t.jsx("h3",{className:"text-2xl md:text-3xl font-bold mb-6 uppercase tracking-wide relative z-10",children:"Clarity is everything."}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 text-lg relative z-10",children:[t.jsx("p",{children:"Good design doesn't scream. It works."}),t.jsx("p",{children:"Good code doesn't show off. It ships."})]})]})}),t.jsx("div",{className:"col-span-12 lg:col-span-4",children:(0,t.jsxs)("div",{className:"h-full bg-bauhaus-yellow rounded-3xl flex items-center justify-center relative overflow-hidden",children:[t.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-20",children:t.jsx(o.IJ,{size:"xl",color:"black",className:"w-20 h-20",animationPreset:"gentle",animationIndex:14})}),t.jsx("div",{className:"text-6xl font-bold text-bauhaus-black relative z-10",children:"01"})]})}),t.jsx("div",{className:"col-span-12 lg:col-span-3",children:(0,t.jsxs)("div",{className:"h-full bg-bauhaus-blue rounded-3xl flex items-center justify-center relative overflow-hidden",children:[t.jsx("div",{className:"absolute bottom-2 right-2",children:t.jsx(o._V,{size:"sm",color:"white",direction:"up",className:"w-4 h-4",animationPreset:"pulse",animationIndex:15})}),t.jsx("div",{className:"text-4xl font-bold text-white relative z-10",children:"02"})]})}),t.jsx("div",{className:"col-span-12 lg:col-span-9",children:(0,t.jsxs)("div",{className:"bg-brand-background border-3 border-bauhaus-black p-8 rounded-3xl relative overflow-hidden",children:[t.jsx("div",{className:"absolute inset-0 opacity-40",children:t.jsx(o.i6,{className:"w-full h-full text-black",opacity:"default",animationPreset:"subtle",animationIndex:16})}),t.jsx("div",{className:"absolute top-4 right-4",children:t.jsx(o.U9,{width:"sm",height:"xl",color:"blue",className:"w-2 h-16",animationPreset:"gentle",animationIndex:17})}),t.jsx("h3",{className:"text-2xl md:text-3xl font-bold mb-6 uppercase tracking-wide relative z-10",children:"Small teams move faster."}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 text-lg relative z-10",children:[t.jsx("p",{children:"You work directly with the people doing the work."}),t.jsx("p",{children:"No account managers. No middle layers. No miscommunication."})]})]})}),t.jsx("div",{className:"col-span-12",children:(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-6",children:[t.jsx("div",{className:"col-span-12 lg:col-span-2",children:(0,t.jsxs)("div",{className:"h-full bg-bauhaus-red rounded-3xl flex items-center justify-center relative overflow-hidden",children:[t.jsx("div",{className:"absolute top-2 left-2",children:t.jsx(o.IJ,{size:"sm",color:"white",className:"w-4 h-4",animationPreset:"gentle",animationIndex:18})}),t.jsx("div",{className:"text-4xl font-bold text-white relative z-10",children:"03"})]})}),t.jsx("div",{className:"col-span-12 lg:col-span-10",children:(0,t.jsxs)("div",{className:"bg-bauhaus-yellow text-bauhaus-black p-8 md:p-12 rounded-3xl relative overflow-hidden",children:[t.jsx("div",{className:"absolute inset-0 opacity-30",children:t.jsx(o.i6,{className:"w-full h-full text-black",opacity:"default",animationPreset:"drift",animationIndex:19})}),t.jsx("div",{className:"absolute bottom-4 right-4",children:t.jsx(o.U9,{width:"md",height:"sm",color:"black",className:"w-8 h-4",animationPreset:"gentle",animationIndex:20})}),t.jsx("h3",{className:"text-2xl md:text-3xl font-bold mb-6 uppercase tracking-wide relative z-10",children:"Function first, aesthetics second."}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 text-lg relative z-10",children:[t.jsx("p",{children:"Beauty is great. Usability is greater."}),t.jsx("p",{children:"We never choose one at the cost of the other."})]})]})})]})})]})]}),t.jsx("div",{className:"absolute top-20 right-20 opacity-30",children:t.jsx(o.U9,{width:"lg",height:"xl",color:"red",className:"w-16 h-80",animationPreset:"drift",animationIndex:21})}),t.jsx("div",{className:"absolute bottom-20 left-20 opacity-40",children:t.jsx(o.IJ,{size:"xl",color:"blue",className:"w-32 h-32",animationPreset:"gentle",animationIndex:22})})]}),(0,t.jsxs)("section",{className:"animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-bauhaus-black text-brand-background overflow-hidden",children:[(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-6 mb-16",children:[t.jsx("div",{className:"col-span-12 lg:col-span-8",children:(0,t.jsxs)("h2",{className:"text-6xl md:text-7xl lg:text-8xl font-bold leading-none",children:["WHY WE",t.jsx("br",{}),t.jsx("span",{className:"text-bauhaus-yellow",children:"EXIST"})]})}),t.jsx("div",{className:"col-span-12 lg:col-span-4 flex items-end justify-end",children:t.jsx(o.IJ,{size:"xl",color:"red",className:"w-24 h-24",animationPreset:"gentle",animationIndex:23})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-6",children:[t.jsx("div",{className:"col-span-12 lg:col-span-4",children:(0,t.jsxs)("div",{className:"bg-bauhaus-red text-white p-8 h-full rounded-3xl flex flex-col justify-between relative overflow-hidden",children:[t.jsx("div",{className:"absolute inset-0 opacity-20",children:t.jsx(o.i6,{className:"w-full h-full text-white",opacity:"default",animationPreset:"subtle",animationIndex:24})}),t.jsx("div",{className:"text-8xl font-bold opacity-20 relative z-10",children:"01"}),t.jsx("p",{className:"text-lg leading-tight relative z-10",children:"Because the internet is full of websites that say too much and do too little."})]})}),t.jsx("div",{className:"col-span-12 lg:col-span-4",children:(0,t.jsxs)("div",{className:"bg-brand-background text-bauhaus-black border-3 border-bauhaus-yellow p-8 h-full rounded-3xl flex flex-col justify-between relative overflow-hidden",children:[t.jsx("div",{className:"absolute inset-0 opacity-40",children:t.jsx(o.i6,{className:"w-full h-full text-black",opacity:"default",animationPreset:"drift",animationIndex:25})}),t.jsx("div",{className:"absolute top-4 right-4",children:t.jsx(o.IJ,{size:"md",color:"yellow",className:"w-8 h-8",animationPreset:"gentle",animationIndex:26})}),t.jsx("div",{className:"text-8xl font-bold text-bauhaus-yellow opacity-50 relative z-10",children:"02"}),t.jsx("p",{className:"text-lg leading-tight relative z-10",children:"Because bloated codebases and unnecessary meetings waste everyone's time."})]})}),t.jsx("div",{className:"col-span-12 lg:col-span-4",children:(0,t.jsxs)("div",{className:"bg-bauhaus-blue text-white p-8 h-full rounded-3xl flex flex-col justify-between relative overflow-hidden",children:[t.jsx("div",{className:"absolute inset-0 opacity-20",children:t.jsx(o.i6,{className:"w-full h-full text-white",opacity:"default",animationPreset:"drift",animationIndex:27})}),t.jsx("div",{className:"absolute bottom-4 left-4",children:t.jsx(o._V,{size:"sm",color:"white",direction:"up",className:"w-4 h-4",animationPreset:"pulse",animationIndex:28})}),t.jsx("div",{className:"text-8xl font-bold opacity-20 relative z-10",children:"03"}),t.jsx("p",{className:"text-lg leading-tight relative z-10",children:"Because you shouldn't have to choose between good design and fast development."})]})}),t.jsx("div",{className:"col-span-12 mt-8",children:(0,t.jsxs)("div",{className:"bg-bauhaus-yellow text-bauhaus-black p-12 text-center rounded-3xl relative overflow-hidden",children:[t.jsx("div",{className:"absolute inset-0 opacity-30",children:t.jsx(o.i6,{className:"w-full h-full text-black",opacity:"default",animationPreset:"subtle",animationIndex:29})}),t.jsx("div",{className:"absolute top-4 left-1/2 transform -translate-x-1/2 opacity-50",children:t.jsx(o.U9,{width:"xl",height:"sm",color:"black",className:"w-16 h-4",animationPreset:"gentle",animationIndex:30})}),t.jsx("p",{className:"text-2xl md:text-3xl font-bold uppercase tracking-wide relative z-10",children:"We're here to cut through the noise and build the right thing, the right way."})]})})]})]}),t.jsx("div",{className:"absolute left-0 top-1/3 opacity-40",children:t.jsx(o.U9,{width:"sm",height:"xl",color:"yellow",className:"w-8 h-40",animationPreset:"drift",animationIndex:31})}),t.jsx("div",{className:"absolute right-0 bottom-1/3 opacity-30",children:t.jsx(o.U9,{width:"md",height:"xl",color:"red",className:"w-12 h-60",animationPreset:"gentle",animationIndex:32})})]}),(0,t.jsxs)("section",{className:"animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background overflow-hidden",children:[(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-6 mb-16",children:[t.jsx("div",{className:"col-span-12 lg:col-span-6",children:(0,t.jsxs)("h2",{className:"text-5xl md:text-6xl font-bold leading-none",children:["HOW WE",t.jsx("br",{}),t.jsx("span",{className:"text-bauhaus-blue",children:"WORK"})]})}),t.jsx("div",{className:"col-span-12 lg:col-span-6 flex items-center justify-end",children:t.jsx(o.U9,{width:"xl",height:"sm",color:"black",className:"w-32 h-8",animationPreset:"gentle",animationIndex:33})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-6",children:[t.jsx("div",{className:"col-span-12 lg:col-span-6",children:(0,t.jsxs)("div",{className:"bg-bauhaus-black text-brand-background p-8 md:p-12 rounded-3xl relative overflow-hidden min-h-[280px] flex flex-col justify-between",children:[t.jsx("div",{className:"absolute inset-0 opacity-30",children:t.jsx(o.i6,{className:"w-full h-full text-brand-background",opacity:"default",animationPreset:"drift",animationIndex:34})}),t.jsx("div",{className:"absolute top-4 left-4",children:t.jsx(o.IJ,{size:"lg",color:"red",className:"w-16 h-16",animationPreset:"gentle",animationIndex:35})}),(0,t.jsxs)("div",{className:"text-right relative z-10",children:[t.jsx("div",{className:"text-6xl font-bold text-bauhaus-red mb-4",children:"01"}),t.jsx("h3",{className:"text-xl md:text-2xl font-bold uppercase tracking-wide leading-tight",children:"We listen first."})]})]})}),t.jsx("div",{className:"col-span-12 lg:col-span-3",children:(0,t.jsxs)("div",{className:"bg-bauhaus-yellow text-bauhaus-black p-8 rounded-3xl flex flex-col justify-between relative overflow-hidden min-h-[280px]",children:[t.jsx("div",{className:"absolute inset-0 opacity-30",children:t.jsx(o.i6,{className:"w-full h-full text-black",opacity:"default",animationPreset:"subtle",animationIndex:36})}),t.jsx("div",{className:"text-6xl font-bold relative z-10",children:"02"}),t.jsx("h3",{className:"text-lg font-bold uppercase tracking-wide relative z-10 leading-tight",children:"We distill ideas into clear, minimal interfaces."})]})}),t.jsx("div",{className:"col-span-12 lg:col-span-3",children:(0,t.jsxs)("div",{className:"bg-bauhaus-blue text-white p-8 rounded-3xl flex flex-col justify-between relative overflow-hidden min-h-[280px]",children:[t.jsx("div",{className:"absolute inset-0 opacity-20",children:t.jsx(o.i6,{className:"w-full h-full text-white",opacity:"default",animationPreset:"drift",animationIndex:37})}),t.jsx("div",{className:"absolute bottom-4 right-4",children:t.jsx(o._V,{size:"sm",color:"white",direction:"up",className:"w-4 h-4",animationPreset:"pulse",animationIndex:38})}),t.jsx("div",{className:"text-6xl font-bold relative z-10",children:"03"}),t.jsx("h3",{className:"text-lg font-bold uppercase tracking-wide relative z-10 leading-tight",children:"We build with clean systems, not hacks."})]})}),t.jsx("div",{className:"col-span-12",children:(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-6",children:[t.jsx("div",{className:"col-span-12 lg:col-span-2",children:(0,t.jsxs)("div",{className:"h-full bg-bauhaus-red rounded-3xl flex items-center justify-center relative overflow-hidden",children:[t.jsx("div",{className:"absolute top-2 left-2",children:t.jsx(o.IJ,{size:"sm",color:"white",className:"w-4 h-4",animationPreset:"gentle",animationIndex:39})}),t.jsx("div",{className:"text-6xl font-bold text-white relative z-10",children:"04"})]})}),t.jsx("div",{className:"col-span-12 lg:col-span-10",children:(0,t.jsxs)("div",{className:"bg-brand-background border-3 border-bauhaus-black p-8 md:p-12 rounded-3xl flex items-center relative overflow-hidden",children:[t.jsx("div",{className:"absolute inset-0 opacity-40",children:t.jsx(o.i6,{className:"w-full h-full text-black",opacity:"default",animationPreset:"subtle",animationIndex:40})}),t.jsx("h3",{className:"text-xl md:text-2xl font-bold uppercase tracking-wide relative z-10",children:"We don't outsource. We don't upsell. We don't hide behind jargon."})]})})]})})]})]}),t.jsx("div",{className:"absolute top-20 left-20 opacity-30",children:t.jsx(o.U9,{width:"sm",height:"xl",color:"red",className:"w-4 h-60",animationPreset:"drift",animationIndex:41})}),t.jsx("div",{className:"absolute bottom-20 right-20 opacity-40",children:t.jsx(o.U9,{width:"xl",height:"sm",color:"blue",className:"w-40 h-4",animationPreset:"gentle",animationIndex:42})})]}),(0,t.jsxs)("section",{className:"animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-bauhaus-black text-brand-background overflow-hidden",children:[t.jsx("div",{className:"max-w-7xl mx-auto",children:(0,t.jsxs)("div",{className:"grid grid-cols-12 gap-6 items-center",children:[t.jsx("div",{className:"col-span-12 lg:col-span-4",children:(0,t.jsxs)("div",{className:"relative h-80",children:[t.jsx("div",{className:"absolute top-0 left-0",children:t.jsx(o.IJ,{size:"xl",color:"red",className:"w-24 h-24",animationPreset:"gentle",animationIndex:43})}),t.jsx("div",{className:"absolute top-16 right-8",children:t.jsx(o.U9,{width:"xl",height:"sm",color:"yellow",className:"w-32 h-8",animationPreset:"drift",animationIndex:44})}),t.jsx("div",{className:"absolute bottom-16 left-8",children:t.jsx(o.U9,{width:"lg",height:"xl",color:"blue",className:"w-16 h-32",animationPreset:"gentle",animationIndex:45})}),t.jsx("div",{className:"absolute bottom-0 right-0",children:t.jsx(o.IJ,{size:"xl",color:"white",className:"w-20 h-20",animationPreset:"pulse",animationIndex:46})})]})}),t.jsx("div",{className:"col-span-12 lg:col-span-8",children:(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("h2",{className:"text-5xl md:text-6xl font-bold leading-none uppercase tracking-wide",children:["Want to work",t.jsx("br",{}),t.jsx("span",{className:"text-bauhaus-yellow",children:"with us?"})]}),(0,t.jsxs)("div",{className:"flex flex-col text-lg",children:[t.jsx("p",{children:"We like projects that are sharp, fast, and meaningful."}),t.jsx("p",{children:"If that's what you've got, we should talk."})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[t.jsx(n(),{href:"/contact",className:"bg-bauhaus-red text-white px-8 py-4 rounded-3xl font-bold uppercase tracking-wide hover:bg-red-700 transition-colors",children:"Start Your Project"}),t.jsx(o.U9,{width:"lg",height:"sm",color:"yellow",className:"w-16 h-4",animationPreset:"gentle",animationIndex:47})]})]})})]})}),t.jsx("div",{className:"absolute top-20 right-20 opacity-30",children:t.jsx(o.U9,{width:"md",height:"xl",color:"yellow",className:"w-12 h-40",animationPreset:"drift",animationIndex:48})}),t.jsx("div",{className:"absolute bottom-20 left-20 opacity-40",children:t.jsx(o.U9,{width:"xl",height:"md",color:"red",className:"w-32 h-12",animationPreset:"gentle",animationIndex:49})}),t.jsx("div",{className:"absolute top-1/3 left-1/4 opacity-50",children:t.jsx(o.IJ,{size:"sm",color:"white",className:"w-8 h-8",animationPreset:"pulse",animationIndex:50})}),t.jsx("div",{className:"absolute bottom-1/3 right-1/3 opacity-40",children:t.jsx(o.U9,{width:"lg",height:"sm",color:"blue",className:"w-16 h-4",animationPreset:"gentle",animationIndex:51})})]})]})}},7621:(e,s,a)=>{"use strict";a.d(s,{Z:()=>h});var t=a(5036),l=a(6843);let i=(0,l.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\layout\Header.tsx`),{__esModule:n,$$typeof:o}=i,r=i.default,d=(0,l.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\layout\Footer.tsx`),{__esModule:c,$$typeof:x}=d,m=d.default;function h({children:e,className:s=""}){return(0,t.jsxs)("div",{className:"min-h-screen flex flex-col",children:[t.jsx(r,{}),t.jsx("main",{className:`flex-1 ${s}`,children:e}),t.jsx(m,{})]})}},8373:(e,s,a)=>{"use strict";a.d(s,{IJ:()=>o,U9:()=>r,_V:()=>d,i6:()=>c});var t=a(6843);let l=(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx`),{__esModule:i,$$typeof:n}=l;l.default,(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedCircle`);let o=(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedSoftCircle`);(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedRectangle`);let r=(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedRoundedRectangle`),d=(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedTriangle`);(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedHalfCircle`);let c=(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedSoftGrid`);(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedRoundedRect`),(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedPill`),(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedBlob`),(0,t.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedQuarterCircle`)}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[638,644,30,366],()=>a(7915));module.exports=t})();