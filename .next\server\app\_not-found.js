(()=>{var e={};e.id=165,e.ids=[165],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},5092:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var s=n(482),a=n(9108),r=n(2563),i=n.n(r),o=n(8300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);n.d(t,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.t.bind(n,9361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(n.bind(n,7843)),"C:\\Users\\<USER>\\Projects\\navhaus\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,9361,23)),"next/dist/client/components/not-found-error"]}],u=[],c="/_not-found",m={require:n,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/_not-found",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4862:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,2583,23)),Promise.resolve().then(n.t.bind(n,6840,23)),Promise.resolve().then(n.t.bind(n,8771,23)),Promise.resolve().then(n.t.bind(n,3225,23)),Promise.resolve().then(n.t.bind(n,9295,23)),Promise.resolve().then(n.t.bind(n,3982,23))},7731:(e,t,n)=>{Promise.resolve().then(n.bind(n,3532))},3532:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});var s=n(3729);function a(){return(0,s.useEffect)(()=>{},[]),null}},7843:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>c,metadata:()=>u});var s=n(5036),a=n(448),r=n.n(a);n(5023);let i=(0,n(6843).createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\performance\PerformanceMonitor.tsx`),{__esModule:o,$$typeof:l}=i,d=i.default,u={title:{default:"navhaus | what matters, made real",template:"%s | navhaus"},description:"Navhaus is a design and development studio that builds bold, efficient, and meaningful digital experiences - nothing more, nothing less.",keywords:["web design","web development","digital agency","wordpress","web apps","navhaus"],authors:[{name:"Navhaus"}],creator:"Navhaus",publisher:"Navhaus",metadataBase:new URL("https://navhaus.com"),alternates:{canonical:"/"},openGraph:{title:"navhaus | what matters, made real",description:"Navhaus is a design and development studio that builds bold, efficient, and meaningful digital experiences - nothing more, nothing less.",url:"https://navhaus.com",siteName:"Navhaus",images:[{url:"/images/og-image.png",width:1200,height:630,alt:"Navhaus - what matters, made real"}],locale:"en_US",type:"website"},twitter:{card:"summary_large_image",title:"navhaus | what matters, made real",description:"Navhaus is a design and development studio that builds bold, efficient, and meaningful digital experiences - nothing more, nothing less.",images:["/images/og-image.png"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},icons:{icon:"/images/icon.png",shortcut:"/images/icon.png",apple:"/images/icon.png"}};function c({children:e}){return(0,s.jsxs)("html",{lang:"en",className:r().variable,children:[s.jsx("head",{children:s.jsx("link",{rel:"dns-prefetch",href:"https://fonts.googleapis.com"})}),(0,s.jsxs)("body",{className:r().className,children:[s.jsx(d,{}),e]})]})}},5023:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),s=t.X(0,[638,644],()=>n(5092));module.exports=s})();